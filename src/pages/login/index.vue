<route lang="jsonc" type="page">
{
  "layout": "default",
  "needLogin": false,
  "style": {
    "navigationBarTitleText": "登录",
    // "navigationStyle": "custom"
  }
}
</route>

<script lang="ts" setup>
import { useMessage } from 'wot-design-uni'
import { useUserStore } from '@/store/user'
import { toast } from '@/utils/toast'

const userStore = useUserStore()

const message = useMessage()

// 表单数据
const loginForm = ref({
  username: '',
  password: '',
})

// 加载状态
const loading = ref(false)

function handleClear() {
  uni.setStorageSync('loginForm', loginForm.value)
}

// 登录处理
async function handleLogin() {
  uni.setStorageSync('loginForm', loginForm.value)
  // 表单验证
  if (!loginForm.value.username.trim()) {
    toast.error('请输入账号')
    return
  }

  if (!loginForm.value.password.trim()) {
    toast.error('请输入密码')
    return
  }

  loading.value = true

  try {
    // 构造登录参数，不需要验证码时传空值
    const loginData = {
      username: loginForm.value.username,
      password: loginForm.value.password,
      code: '',
      uuid: '',
    }

    const res = await userStore.login(loginData)
    if (res.code === 200) {
      uni.reLaunch({
        url: '/pages/index/index',
      })
    }
    else {
      message.alert({
        title: '登录失败',
        msg: res.msg,
      })
    }
  }
  catch (error) {
    message.alert({
      title: '登录失败',
      msg: '登录失败，请检查账号密码',
    })
  }
  finally {
    loading.value = false
  }
}

onMounted(() => {
  const savedForm = uni.getStorageSync('loginForm')
  console.log(savedForm)
  if (savedForm) {
    loginForm.value = savedForm
  }
})
</script>

<template>
  <view>
    <view>
      <view mb-20 mt-20>
        <text mt-20px b-x-0 border-b-5 border-t-10rpx b-b-blue b-t-blue b-solid>用户登录</text>
      </view>

      <view>
        <wd-input
          v-model="loginForm.username"
          placeholder="请输入账号"
          clearable
          :disabled="loading"
          @clear="handleClear"
        />
      </view>

      <view>
        <wd-input
          v-model="loginForm.password"
          placeholder="请输入登录密码"
          :show-password="true"
          clearable
          :disabled="loading"
          @clear="handleClear"
        />
      </view>

      <wd-button
        type="primary"
        :loading="loading"
        :disabled="loading"
        @click="handleLogin"
      >
        {{ loading ? '登录中...' : '登录' }}
      </wd-button>
    </view>
  </view>
</template>

<style lang="scss" scoped>
// 无需额外样式，使用内联样式
</style>
